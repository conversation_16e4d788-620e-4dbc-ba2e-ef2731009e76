{"name": "bitcoin-trading-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-prod": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|.gitignore|package-lock.json|vite-env.d.ts' > docs/file-system.md"}, "dependencies": {"ethers": "^6.15.0", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/socket.io-client": "^1.4.36", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}