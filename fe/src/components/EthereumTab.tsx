import React, { useState, useEffect } from 'react';
import {
  EthereumRealtimeData,
  EthereumBlock,
  EthereumLog,
  EthereumGasPrice,
  EthereumNetworkStats
} from '../types/ethereum';
import {
  formatEther,
  formatGwei,
  formatAddress,
  formatTxHash,
  formatBlockNumber,
  formatEthereumTimestamp,
  getTimeAgo,
  formatGasUsage,
  getGasStatus,
  estimateConfirmationTime,
  getTransactionType,
  formatBlockGasUtilization,
  getNetworkStatusColor
} from '../utils/ethereumUtils';

// ============================================
// INTERFACES
// ============================================

interface EthereumTabProps {
  ethereumData: EthereumRealtimeData | null;
  refreshEthereum: () => void;
  showEducationalPanel: boolean;
  setShowEducationalPanel: (show: boolean) => void;
}

// ============================================
// COMPONENTE PRINCIPAL
// ============================================

const EthereumTab: React.FC<EthereumTabProps> = ({
  ethereumData,
  refreshEthereum,
  showEducationalPanel,
  setShowEducationalPanel
}) => {
  const [activeSubTab, setActiveSubTab] = useState<'overview' | 'blocks' | 'transactions' | 'logs' | 'gas'>('overview');

  return (
    <div className="tab-content">
      {/* Header */}
      <div className="section-header">
        <h2>🔷 Ethereum Network Analytics</h2>
        <div className="action-buttons">
          <button onClick={refreshEthereum} className="btn-secondary">
            🔄 Actualizar Datos
          </button>
          <button
            onClick={() => setShowEducationalPanel(!showEducationalPanel)}
            className="btn-educational"
          >
            📚 ¿Qué es Ethereum?
          </button>
        </div>
      </div>

      {/* Educational Panel */}
      {showEducationalPanel && (
        <div className="educational-panel">
          <h3>🎓 Entendiendo Ethereum</h3>
          <div className="educational-content">
            <div className="educational-item">
              <h4>🔷 ¿Qué es Ethereum?</h4>
              <p>Ethereum es una plataforma blockchain descentralizada que permite ejecutar contratos inteligentes y aplicaciones descentralizadas (dApps).</p>
            </div>
            <div className="educational-item">
              <h4>⛽ Gas</h4>
              <p>El gas es la unidad que mide el costo computacional de las operaciones en Ethereum. Se paga en ETH.</p>
            </div>
            <div className="educational-item">
              <h4>📋 Logs/Eventos</h4>
              <p>Los logs son eventos emitidos por contratos inteligentes durante la ejecución de transacciones.</p>
            </div>
            <div className="educational-item">
              <h4>🔄 Mempool</h4>
              <p>El mempool contiene transacciones pendientes esperando ser incluidas en el próximo bloque.</p>
            </div>
          </div>
        </div>
      )}

      {/* Sub-navigation */}
      <div className="sub-tabs">
        <button
          className={`sub-tab ${activeSubTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('overview')}
        >
          📊 Resumen
        </button>
        <button
          className={`sub-tab ${activeSubTab === 'blocks' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('blocks')}
        >
          🧱 Bloques
        </button>
        <button
          className={`sub-tab ${activeSubTab === 'transactions' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('transactions')}
        >
          💸 Transacciones
        </button>
        <button
          className={`sub-tab ${activeSubTab === 'logs' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('logs')}
        >
          📋 Eventos/Logs
        </button>
        <button
          className={`sub-tab ${activeSubTab === 'gas' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('gas')}
        >
          ⛽ Gas Tracker
        </button>
      </div>

      {/* Content */}
      <div className="tab-content-wrapper">
        {activeSubTab === 'overview' && (
          <EthereumOverview ethereumData={ethereumData} />
        )}
        {activeSubTab === 'blocks' && (
          <EthereumBlocks ethereumData={ethereumData} />
        )}
        {activeSubTab === 'transactions' && (
          <EthereumTransactions ethereumData={ethereumData} />
        )}
        {activeSubTab === 'logs' && (
          <EthereumLogs ethereumData={ethereumData} />
        )}
        {activeSubTab === 'gas' && (
          <EthereumGasTracker ethereumData={ethereumData} />
        )}
      </div>
    </div>
  );
};

// ============================================
// COMPONENTE OVERVIEW
// ============================================

const EthereumOverview: React.FC<{ ethereumData: EthereumRealtimeData | null }> = ({ ethereumData }) => {
  if (!ethereumData) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Cargando datos de Ethereum...</p>
      </div>
    );
  }

  const { latestBlock, gasPrice, networkStats, pendingTransactions } = ethereumData;

  return (
    <div className="overview-grid">
      {/* Latest Block */}
      <div className="metric-card">
        <h3>🧱 Último Bloque</h3>
        {latestBlock ? (
          <div className="metric-content">
            <div className="metric-value">#{formatBlockNumber(latestBlock.number)}</div>
            <div className="metric-details">
              <p>Hash: {formatTxHash(latestBlock.hash)}</p>
              <p>Tiempo: {getTimeAgo(latestBlock.timestamp)}</p>
              <p>Transacciones: {Array.isArray(latestBlock.transactions) ? latestBlock.transactions.length : 0}</p>
              {latestBlock.gasUsed && latestBlock.gasLimit && (
                <p>Gas: {formatGasUsage(latestBlock.gasUsed, latestBlock.gasLimit)}</p>
              )}
            </div>
          </div>
        ) : (
          <div className="metric-value">No disponible</div>
        )}
      </div>

      {/* Gas Price */}
      <div className="metric-card">
        <h3>⛽ Precio del Gas</h3>
        {gasPrice ? (
          <div className="metric-content">
            <div className="metric-value">
              {gasPrice.gasPrice ? `${formatGwei(gasPrice.gasPrice)} Gwei` : 'N/A'}
            </div>
            <div className="metric-details">
              {gasPrice.maxFeePerGas && (
                <p>Max Fee: {formatGwei(gasPrice.maxFeePerGas)} Gwei</p>
              )}
              {gasPrice.maxPriorityFeePerGas && (
                <p>Priority: {formatGwei(gasPrice.maxPriorityFeePerGas)} Gwei</p>
              )}
              {gasPrice.gasPrice && (
                <p className={`status-${getGasStatus(gasPrice.gasPrice).color}`}>
                  {getGasStatus(gasPrice.gasPrice).description}
                </p>
              )}
            </div>
          </div>
        ) : (
          <div className="metric-value">No disponible</div>
        )}
      </div>

      {/* Network Stats */}
      <div className="metric-card">
        <h3>🌐 Red</h3>
        {networkStats ? (
          <div className="metric-content">
            <div className="metric-value">{networkStats.networkName}</div>
            <div className="metric-details">
              <p>Chain ID: {networkStats.chainId}</p>
              <p>Bloque: #{formatBlockNumber(networkStats.latestBlock)}</p>
              <p className={`status-${getNetworkStatusColor(networkStats.pendingTransactionsCount)}`}>
                Pendientes: {networkStats.pendingTransactionsCount.toLocaleString()}
              </p>
            </div>
          </div>
        ) : (
          <div className="metric-value">No disponible</div>
        )}
      </div>

      {/* Pending Transactions */}
      <div className="metric-card">
        <h3>🔄 Mempool</h3>
        <div className="metric-content">
          <div className="metric-value">{pendingTransactions.length.toLocaleString()}</div>
          <div className="metric-details">
            <p>Transacciones pendientes</p>
            <p className={`status-${getNetworkStatusColor(pendingTransactions.length)}`}>
              Estado: {pendingTransactions.length < 1000 ? 'Normal' :
                      pendingTransactions.length < 5000 ? 'Ocupado' :
                      pendingTransactions.length < 10000 ? 'Congestionado' : 'Muy Congestionado'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================
// COMPONENTE BLOCKS
// ============================================

const EthereumBlocks: React.FC<{ ethereumData: EthereumRealtimeData | null }> = ({ ethereumData }) => {
  if (!ethereumData?.latestBlock) {
    return (
      <div className="loading-container">
        <p>No hay datos de bloques disponibles</p>
      </div>
    );
  }

  const { latestBlock } = ethereumData;
  const gasUtilization = formatBlockGasUtilization(latestBlock.gasUsed, latestBlock.gasLimit);

  return (
    <div className="blocks-container">
      <div className="block-card">
        <div className="block-header">
          <h3>🧱 Bloque #{formatBlockNumber(latestBlock.number)}</h3>
          <span className="block-time">{formatEthereumTimestamp(latestBlock.timestamp)}</span>
        </div>

        <div className="block-details">
          <div className="detail-row">
            <span className="label">Hash:</span>
            <span className="value">{latestBlock.hash}</span>
          </div>
          <div className="detail-row">
            <span className="label">Parent Hash:</span>
            <span className="value">{latestBlock.parentHash}</span>
          </div>
          <div className="detail-row">
            <span className="label">Transacciones:</span>
            <span className="value">{Array.isArray(latestBlock.transactions) ? latestBlock.transactions.length : 0}</span>
          </div>
          <div className="detail-row">
            <span className="label">Gas Usado:</span>
            <span className="value">{formatGasUsage(latestBlock.gasUsed, latestBlock.gasLimit)}</span>
          </div>
          <div className="detail-row">
            <span className="label">Utilización:</span>
            <span className={`value status-${gasUtilization.color}`}>
              {gasUtilization.percentage}% ({gasUtilization.status})
            </span>
          </div>
          {latestBlock.baseFeePerGas && (
            <div className="detail-row">
              <span className="label">Base Fee:</span>
              <span className="value">{formatGwei(latestBlock.baseFeePerGas)} Gwei</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// ============================================
// COMPONENTE TRANSACTIONS
// ============================================

const EthereumTransactions: React.FC<{ ethereumData: EthereumRealtimeData | null }> = ({ ethereumData }) => {
  if (!ethereumData?.pendingTransactions.length) {
    return (
      <div className="loading-container">
        <p>No hay transacciones pendientes disponibles</p>
      </div>
    );
  }

  const { pendingTransactions } = ethereumData;

  return (
    <div className="transactions-container">
      <div className="transactions-header">
        <h3>💸 Transacciones Pendientes ({pendingTransactions.length})</h3>
        <p>Últimas transacciones en el mempool esperando confirmación</p>
      </div>

      <div className="transactions-list">
        {pendingTransactions.slice(0, 20).map((txHash, index) => (
          <div key={txHash} className="transaction-item">
            <div className="tx-info">
              <span className="tx-hash">{formatTxHash(txHash)}</span>
              <span className="tx-status pending">Pendiente</span>
            </div>
            <div className="tx-details">
              <span className="tx-position">Posición #{index + 1} en mempool</span>
            </div>
          </div>
        ))}
      </div>

      {pendingTransactions.length > 20 && (
        <div className="transactions-footer">
          <p>Mostrando 20 de {pendingTransactions.length} transacciones pendientes</p>
        </div>
      )}
    </div>
  );
};

// ============================================
// COMPONENTE LOGS
// ============================================

const EthereumLogs: React.FC<{ ethereumData: EthereumRealtimeData | null }> = ({ ethereumData }) => {
  if (!ethereumData?.logs.length) {
    return (
      <div className="loading-container">
        <p>No hay logs/eventos disponibles</p>
      </div>
    );
  }

  const { logs } = ethereumData;

  return (
    <div className="logs-container">
      <div className="logs-header">
        <h3>📋 Eventos/Logs Recientes ({logs.length})</h3>
        <p>Eventos emitidos por contratos inteligentes</p>
      </div>

      <div className="logs-list">
        {logs.map((log, index) => (
          <div key={`${log.transactionHash}-${log.logIndex}`} className="log-item">
            <div className="log-header">
              <span className="log-contract">{formatAddress(log.address)}</span>
              <span className="log-block">Bloque #{formatBlockNumber(log.blockNumber)}</span>
            </div>

            <div className="log-details">
              <div className="log-tx">
                <span className="label">Tx:</span>
                <span className="value">{formatTxHash(log.transactionHash)}</span>
              </div>

              {log.topics.length > 0 && (
                <div className="log-topics">
                  <span className="label">Topics:</span>
                  <div className="topics-list">
                    {log.topics.slice(0, 2).map((topic, i) => (
                      <span key={i} className="topic">{topic.slice(0, 10)}...</span>
                    ))}
                    {log.topics.length > 2 && <span className="topic">+{log.topics.length - 2} más</span>}
                  </div>
                </div>
              )}

              {log.data && log.data !== '0x' && (
                <div className="log-data">
                  <span className="label">Data:</span>
                  <span className="value">{log.data.slice(0, 20)}...</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ============================================
// COMPONENTE GAS TRACKER
// ============================================

const EthereumGasTracker: React.FC<{ ethereumData: EthereumRealtimeData | null }> = ({ ethereumData }) => {
  if (!ethereumData?.gasPrice) {
    return (
      <div className="loading-container">
        <p>No hay datos de gas disponibles</p>
      </div>
    );
  }

  const { gasPrice } = ethereumData;
  const gasStatus = gasPrice.gasPrice ? getGasStatus(gasPrice.gasPrice) : null;

  return (
    <div className="gas-tracker-container">
      <div className="gas-tracker-header">
        <h3>⛽ Gas Tracker</h3>
        <p>Información actual sobre precios de gas en Ethereum</p>
      </div>

      <div className="gas-metrics">
        {gasPrice.gasPrice && (
          <div className="gas-metric-card">
            <h4>💰 Precio Base</h4>
            <div className="gas-value">
              {formatGwei(gasPrice.gasPrice)} Gwei
            </div>
            {gasStatus && (
              <div className={`gas-status status-${gasStatus.color}`}>
                {gasStatus.description}
              </div>
            )}
            <div className="gas-estimate">
              {estimateConfirmationTime(gasPrice.gasPrice)}
            </div>
          </div>
        )}

        {gasPrice.maxFeePerGas && (
          <div className="gas-metric-card">
            <h4>🔝 Max Fee</h4>
            <div className="gas-value">
              {formatGwei(gasPrice.maxFeePerGas)} Gwei
            </div>
            <div className="gas-description">
              Máximo que pagarás por gas
            </div>
          </div>
        )}

        {gasPrice.maxPriorityFeePerGas && (
          <div className="gas-metric-card">
            <h4>⚡ Priority Fee</h4>
            <div className="gas-value">
              {formatGwei(gasPrice.maxPriorityFeePerGas)} Gwei
            </div>
            <div className="gas-description">
              Propina para mineros
            </div>
          </div>
        )}
      </div>

      <div className="gas-recommendations">
        <h4>💡 Recomendaciones</h4>
        <div className="recommendations-grid">
          <div className="recommendation-card">
            <h5>🐌 Lento ({'>'}10 min)</h5>
            <p>Para transacciones no urgentes</p>
            <div className="rec-gas">
              {gasPrice.gasPrice ? `${Math.max(1, parseFloat(formatGwei(gasPrice.gasPrice)) * 0.8).toFixed(1)} Gwei` : 'N/A'}
            </div>
          </div>

          <div className="recommendation-card">
            <h5>🚀 Estándar (~2-5 min)</h5>
            <p>Para la mayoría de transacciones</p>
            <div className="rec-gas">
              {gasPrice.gasPrice ? `${formatGwei(gasPrice.gasPrice)} Gwei` : 'N/A'}
            </div>
          </div>

          <div className="recommendation-card">
            <h5>⚡ Rápido (~1 min)</h5>
            <p>Para transacciones urgentes</p>
            <div className="rec-gas">
              {gasPrice.gasPrice ? `${(parseFloat(formatGwei(gasPrice.gasPrice)) * 1.2).toFixed(1)} Gwei` : 'N/A'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EthereumTab;
