// ============================================
// TIPOS TYPESCRIPT PARA ETHEREUM
// ============================================

export interface EthereumBlock {
  number: number;
  hash: string;
  timestamp: number;
  gasLimit: number;
  gasUsed: number;
  baseFeePerGas?: number;
  transactions: string[] | EthereumTransaction[];
  parentHash: string;
  miner?: string;
  difficulty?: string;
  totalDifficulty?: string;
  size?: number;
  nonce?: string;
  extraData?: string;
  logsBloom?: string;
  transactionsRoot?: string;
  stateRoot?: string;
  receiptsRoot?: string;
}

export interface EthereumTransaction {
  hash: string;
  blockNumber?: number;
  blockHash?: string;
  transactionIndex?: number;
  from: string;
  to?: string;
  value: string;
  gas: number;
  gasPrice: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  data: string;
  nonce: number;
  type?: number;
  chainId?: number;
  v?: string;
  r?: string;
  s?: string;
}

export interface EthereumTransactionReceipt {
  transactionHash: string;
  blockNumber: number;
  blockHash: string;
  transactionIndex: number;
  from: string;
  to?: string;
  gasUsed: number;
  cumulativeGasUsed: number;
  contractAddress?: string;
  logs: EthereumLog[];
  status: number;
  effectiveGasPrice?: string;
  type?: number;
  logsBloom: string;
}

export interface EthereumLog {
  address: string;
  topics: string[];
  data: string;
  blockNumber: number;
  transactionHash: string;
  transactionIndex: number;
  blockHash: string;
  logIndex: number;
  removed?: boolean;
}

export interface EthereumGasPrice {
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  timestamp: number;
}

export interface EthereumNetworkStats {
  chainId: number;
  networkName: string;
  latestBlock: number;
  gasPrice: EthereumGasPrice;
  pendingTransactionsCount: number;
  timestamp: number;
}

export interface EthereumBalance {
  address: string;
  balance: string;
  blockTag: string;
}

export interface EthereumCallTransaction {
  to?: string;
  from?: string;
  gas?: string;
  gasPrice?: string;
  value?: string;
  data?: string;
}

export interface EthereumLogFilter {
  address?: string | string[];
  topics?: (string | string[] | null)[];
  fromBlock?: string | number;
  toBlock?: string | number;
  blockHash?: string;
}

export interface EthereumRealtimeData {
  latestBlock: EthereumBlock | null;
  pendingTransactions: string[];
  logs: EthereumLog[];
  gasPrice: EthereumGasPrice | null;
  networkStats: EthereumNetworkStats | null;
  lastUpdate: number;
}

export interface EthereumTraceResult {
  action: {
    callType?: string;
    from?: string;
    to?: string;
    gas?: string;
    input?: string;
    value?: string;
  };
  result?: {
    gasUsed?: string;
    output?: string;
  };
  error?: string;
  traceAddress: number[];
  subtraces: number;
  transactionPosition?: number;
  type: string;
}

export interface EthereumDebugTrace {
  gas: number;
  returnValue: string;
  structLogs: {
    pc: number;
    op: string;
    gas: number;
    gasCost: number;
    depth: number;
    stack: string[];
    memory?: string[];
    storage?: { [key: string]: string };
  }[];
}

// Tipos para componentes React
export interface EthereumComponentProps {
  ethereumData: EthereumRealtimeData | null;
  refreshEthereum: () => void;
}

export interface EthereumBlockProps {
  block: EthereumBlock | null;
  showTransactions?: boolean;
}

export interface EthereumTransactionProps {
  transaction: EthereumTransaction;
  receipt?: EthereumTransactionReceipt;
  showDetails?: boolean;
}

export interface EthereumGasPriceProps {
  gasPrice: EthereumGasPrice | null;
  showHistory?: boolean;
}

export interface EthereumLogsProps {
  logs: EthereumLog[];
  maxLogs?: number;
}

export interface EthereumNetworkProps {
  networkStats: EthereumNetworkStats | null;
  showDetails?: boolean;
}

// Tipos para eventos WebSocket
export interface EthereumWebSocketEvents {
  'ethereum-new-block': EthereumBlock;
  'ethereum-new-transaction': string;
  'ethereum-new-log': EthereumLog;
  'ethereum-gas-price': EthereumGasPrice;
  'ethereum-data': EthereumRealtimeData;
  'ethereum-block': EthereumBlock;
  'ethereum-balance': EthereumBalance;
  'ethereum-logs': EthereumLog[];
  'ethereum-subscription-confirmed': {
    type: string;
    filter?: EthereumLogFilter;
  };
  'ethereum-unsubscription-confirmed': {
    subscriptionId: string;
  };
}

// Tipos para formularios y inputs
export interface EthereumAddressInput {
  address: string;
  isValid: boolean;
  error?: string;
}

export interface EthereumTransactionInput {
  to?: string;
  value?: string;
  gas?: string;
  gasPrice?: string;
  data?: string;
}

export interface EthereumLogFilterInput {
  address?: string;
  topics?: string[];
  fromBlock?: string;
  toBlock?: string;
}

// Tipos para estadísticas y métricas
export interface EthereumMetrics {
  blockTime: number; // tiempo promedio entre bloques
  gasUtilization: number; // porcentaje de gas utilizado
  transactionThroughput: number; // transacciones por segundo
  averageGasPrice: number; // precio promedio del gas
  networkHashrate?: string; // hashrate de la red
  difficulty?: string; // dificultad actual
}

export interface EthereumPriceData {
  ethPriceUSD: number;
  priceChange24h: number;
  marketCap: number;
  volume24h: number;
  lastUpdated: number;
}

// Tipos para análisis avanzado
export interface EthereumWhaleActivity {
  largeTransactions: EthereumTransaction[];
  threshold: string; // en ETH
  count24h: number;
  totalValue24h: string;
}

export interface EthereumContractActivity {
  address: string;
  name?: string;
  transactionCount: number;
  gasUsed: number;
  logs: EthereumLog[];
}

// Tipos para configuración
export interface EthereumConfig {
  rpcEndpoint: string;
  wsEndpoint?: string;
  chainId: number;
  networkName: string;
  blockExplorer: string;
  gasTracker?: string;
}

// Tipos para errores
export interface EthereumError {
  code: number;
  message: string;
  data?: any;
}

// Tipos para respuestas de API
export interface EthereumApiResponse<T> {
  success: boolean;
  data?: T;
  error?: EthereumError;
  timestamp: number;
}

// Tipos para subscripciones
export interface EthereumSubscription {
  id: string;
  type: 'newHeads' | 'logs' | 'pendingTransactions';
  filter?: EthereumLogFilter;
  callback: (data: any) => void;
}

// Tipos para el estado del componente
export interface EthereumState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  data: EthereumRealtimeData | null;
  subscriptions: EthereumSubscription[];
}

// Tipos para acciones del reducer
export type EthereumAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_DATA'; payload: EthereumRealtimeData }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'ADD_SUBSCRIPTION'; payload: EthereumSubscription }
  | { type: 'REMOVE_SUBSCRIPTION'; payload: string }
  | { type: 'UPDATE_BLOCK'; payload: EthereumBlock }
  | { type: 'ADD_TRANSACTION'; payload: string }
  | { type: 'ADD_LOG'; payload: EthereumLog }
  | { type: 'UPDATE_GAS_PRICE'; payload: EthereumGasPrice };
