import { useEffect, useState } from 'react'
import io from 'socket.io-client';
import './App.css'

// Importar componentes
import EnhancedBlockchainTab from './components/EnhancedBlockchainTab';
import EthereumTab from './components/EthereumTab';

// Importar tipos de Ethereum
import type { EthereumRealtimeData } from './types/ethereum';

// ============================================
// DEBUG COMPONENT (TEMPORARY)
// ============================================

const DebugPanel = ({ currentBlock, mempoolData, connectionStatus }: any) => {
  const [isOpen, setIsOpen] = useState(false);

  if (!isOpen) {
    return (
      <div className="debug-toggle">
        <button onClick={() => setIsOpen(true)} className="debug-btn">
          🛠️ Debug Info
        </button>
      </div>
    );
  }

  return (
    <div className="debug-panel">
      <div className="debug-header">
        <h3>🛠️ Debug Information</h3>
        <button onClick={() => setIsOpen(false)} className="debug-close">✕</button>
      </div>
      <div className="debug-content">
        <div className="debug-section">
          <h4>Connection Status:</h4>
          <pre>{JSON.stringify(connectionStatus, null, 2)}</pre>
        </div>

        <div className="debug-section">
          <h4>Current Block:</h4>
          <pre>{JSON.stringify(currentBlock, null, 2)}</pre>
        </div>
        <div className="debug-section">
          <h4>Mempool Data:</h4>
          <pre>{JSON.stringify(mempoolData, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
};

// ============================================
// COMPONENTE EDUCATIVO PARA BLOCKCHAIN
// ============================================

const BlockchainEducationalPanel = ({ isOpen, onToggle }: { isOpen: boolean, onToggle: () => void }) => {
  if (!isOpen) {
    return (
      <div className="educational-toggle">
        <button onClick={onToggle} className="educational-btn">
          📚 ¿Qué significa todo esto?
        </button>
      </div>
    );
  }

  return (
    <div className="educational-panel">
      <div className="educational-header">
        <h3>📚 Guía: Entendiendo la Blockchain de Bitcoin</h3>
        <button onClick={onToggle} className="educational-close">✕</button>
      </div>
      <div className="educational-content">
        <div className="educational-section">
          <h4>🧱 ¿Qué es un Bloque?</h4>
          <p>Un bloque es como una "página" del libro contable de Bitcoin que contiene:</p>
          <ul>
            <li><strong>Height:</strong> El número de página (ej: bloque #850,123)</li>
            <li><strong>Hash:</strong> Una "huella digital" única e irrepetible</li>
            <li><strong>Transacciones:</strong> Como "cheques" digitales procesados</li>
            <li><strong>Fees:</strong> Comisiones pagadas a los mineros</li>
            <li><strong>Timestamp:</strong> Momento exacto de creación</li>
          </ul>
        </div>

        <div className="educational-section">
          <h4>📊 ¿Qué es el Mempool?</h4>
          <p>El Mempool es la "sala de espera" de Bitcoin:</p>
          <ul>
            <li><strong>Count:</strong> Transacciones esperando confirmación</li>
            <li><strong>Fees:</strong> Total de comisiones esperando</li>
            <li><strong>Size:</strong> "Peso" total de transacciones pendientes</li>
          </ul>
          <div className="analogy-box">
            <strong>💡 Analogía:</strong> Como la cola de un restaurante - quien paga más propina (fee) es atendido primero
          </div>
        </div>

        <div className="educational-section">
          <h4>💸 ¿Qué son las Transacciones?</h4>
          <ul>
            <li><strong>TXID:</strong> Número de identificación único</li>
            <li><strong>Fee Rate:</strong> Propina por byte (determina velocidad)</li>
            <li><strong>Value:</strong> Cantidad de Bitcoin movida</li>
            <li><strong>VSize:</strong> "Tamaño" de la transacción</li>
          </ul>
        </div>

        <div className="educational-section">
          <h4>🔄 El Proceso Completo</h4>
          <div className="process-flow">
            <div className="flow-step">1. Usuario envía Bitcoin</div>
            <div className="flow-arrow">↓</div>
            <div className="flow-step">2. Va al MEMPOOL (espera)</div>
            <div className="flow-arrow">↓</div>
            <div className="flow-step">3. Mineros crean BLOQUE</div>
            <div className="flow-arrow">↓</div>
            <div className="flow-step">4. Bloque confirmado para siempre</div>
          </div>
        </div>

        <div className="educational-section">
          <h4>🎯 ¿Por qué es importante para trading?</h4>
          <ul>
            <li><strong>Nuevo bloque:</strong> Puede indicar actividad alta en la red</li>
            <li><strong>Mempool lleno:</strong> Congestión = alta demanda</li>
            <li><strong>Fees altos:</strong> Urgencia por mover Bitcoin</li>
            <li><strong>Actividad on-chain:</strong> Flujos hacia/desde exchanges</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// ============================================
// INTERFACES
// ============================================



interface BlockData {
  height: number;
  hash: string;
  timestamp: number;
  txCount: number;
  totalFees: number;
  size: number;
  weight?: number;
  difficulty?: string;
  source: string;
}

interface MempoolData {
  count: number;
  totalFees: number;
  vsize: number;
  timestamp: number;
}

interface Transaction {
  txid: string;
  fee: number;
  vsize: number;
  feeRate: number;
  value: number;
}

interface ConnectionStatus {
  blockchain: boolean;
  server: boolean;
}

// Interfaces para datos avanzados
interface NetworkMetrics {
  hashRate: number;
  hashRateTrend: number;
  difficulty: number;
  nextDifficultyChange: number;
  recommendedFees: any;
  timestamp: number;
}

interface WhaleActivity {
  largeTransactions: any[];
  totalVolume: number;
  count: number;
  alerts: any[];
}

interface Predictions {
  fees?: any;
  sentiment?: any;
  timeframe: string;
}

// ============================================
// MAIN COMPONENT
// ============================================

function App() {
  // Estados de conexión
  const [socket, setSocket] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    blockchain: false,
    server: false
  });
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Estados de datos Blockchain
  const [currentBlock, setCurrentBlock] = useState<BlockData | null>(null);
  const [mempoolData, setMempoolData] = useState<MempoolData | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);

  // Estados de datos avanzados
  const [networkMetrics, setNetworkMetrics] = useState<NetworkMetrics | null>(null);
  const [whaleActivity, setWhaleActivity] = useState<WhaleActivity | null>(null);
  const [predictions, setPredictions] = useState<Predictions | null>(null);

  // Estados de datos Ethereum
  const [ethereumData, setEthereumData] = useState<EthereumRealtimeData | null>(null);

  // Estado de UI
  const [activeTab, setActiveTab] = useState<'blockchain' | 'ethereum'>('blockchain');
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());
  const [showEducationalPanel, setShowEducationalPanel] = useState(false);

  // ============================================
  // SOCKET CONNECTION
  // ============================================

  useEffect(() => {
    console.log('🔌 Attempting to connect to server...');

    const newSocket = io('http://localhost:3001', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    setSocket(newSocket);

    // Connection events
    newSocket.on('connect', () => {
      console.log('✅ Connected to server');
      setConnectionStatus(prev => ({ ...prev, server: true }));
      setConnectionError(null);
    });

    newSocket.on('disconnect', (reason: string) => {
      console.log('❌ Disconnected from server:', reason);
      setConnectionStatus(prev => ({ ...prev, server: false }));
      setConnectionError(`Disconnected: ${reason}`);
    });

    newSocket.on('connect_error', (error: any) => {
      console.error('❌ Connection error:', error.message);
      setConnectionError(`Connection failed: ${error.message}`);
      setConnectionStatus(prev => ({ ...prev, server: false }));
    });





    // ============================================
    // BLOCKCHAIN EVENTS
    // ============================================

    newSocket.on('blockchain-block', (data: BlockData) => {
      setCurrentBlock(data);
      setLastUpdate(Date.now());
    });

    newSocket.on('new-block', (data: BlockData) => {
      setCurrentBlock(data);
      console.log('🎯 New Bitcoin block mined!', data.height);
    });

    newSocket.on('blockchain-mempool', (data: MempoolData) => {
      setMempoolData(data);
    });

    newSocket.on('blockchain-transaction', (data: Transaction) => {
      setRecentTransactions(prev => [data, ...prev.slice(0, 19)]); // Keep last 20
    });

    newSocket.on('blockchain-connection', (status: any) => {
      setConnectionStatus(prev => ({ ...prev, blockchain: status.connected }));
    });

    // Eventos para datos avanzados
    newSocket.on('network-metrics', (data: NetworkMetrics) => {
      setNetworkMetrics(data);
    });

    newSocket.on('whale-activity', (data: WhaleActivity) => {
      setWhaleActivity(data);
    });

    newSocket.on('blockchain-predictions', (data: any) => {
      setPredictions(data);
    });

    // ============================================
    // ETHEREUM EVENTS
    // ============================================

    newSocket.on('ethereum-data', (data: EthereumRealtimeData) => {
      setEthereumData(data);
    });

    newSocket.on('ethereum-new-block', (blockData: any) => {
      setEthereumData(prev => prev ? {
        ...prev,
        latestBlock: blockData,
        lastUpdate: Date.now()
      } : null);
    });

    newSocket.on('ethereum-new-transaction', (txHash: string) => {
      setEthereumData(prev => prev ? {
        ...prev,
        pendingTransactions: [txHash, ...prev.pendingTransactions.slice(0, 99)],
        lastUpdate: Date.now()
      } : null);
    });

    newSocket.on('ethereum-new-log', (logData: any) => {
      setEthereumData(prev => prev ? {
        ...prev,
        logs: [logData, ...prev.logs.slice(0, 49)],
        lastUpdate: Date.now()
      } : null);
    });

    newSocket.on('ethereum-gas-price', (gasPriceData: any) => {
      setEthereumData(prev => prev ? {
        ...prev,
        gasPrice: gasPriceData,
        lastUpdate: Date.now()
      } : null);
    });

    // ============================================
    // INITIAL DATA & COMPATIBILITY
    // ============================================

    newSocket.on('initial-data', (data: any) => {
      if (data.blockchain?.lastBlock) setCurrentBlock(data.blockchain.lastBlock);
      if (data.blockchain?.mempool) setMempoolData(data.blockchain.mempool);
      if (data.blockchain?.recentTransactions) setRecentTransactions(data.blockchain.recentTransactions);
      if (data.blockchain?.networkMetrics) setNetworkMetrics(data.blockchain.networkMetrics);
      if (data.blockchain?.whaleActivity) setWhaleActivity(data.blockchain.whaleActivity);
      if (data.blockchain?.predictions) setPredictions(data.blockchain.predictions);

      // Datos de Ethereum
      if (data.ethereum) setEthereumData(data.ethereum);
    });

    newSocket.on('blockchain-data', (data: any) => {
      if (data.lastBlock) setCurrentBlock(data.lastBlock);
      if (data.mempool) setMempoolData(data.mempool);
      if (data.recentTransactions) setRecentTransactions(data.recentTransactions);
      if (data.networkMetrics) setNetworkMetrics(data.networkMetrics);
      if (data.whaleActivity) setWhaleActivity(data.whaleActivity);
      if (data.predictions) setPredictions(data.predictions);
    });

    // Error handling
    newSocket.on('error', (error: any) => {
      console.error('Socket error:', error);
    });

    // Cleanup
    return () => {
      newSocket.disconnect();
    };
  }, []);

  // ============================================
  // HELPER FUNCTIONS
  // ============================================



  const refreshBlockchain = () => {
    if (socket) {
      socket.emit('request-blockchain-refresh');
    }
  };

  const refreshEthereum = () => {
    if (socket) {
      socket.emit('request-ethereum-data');
    }
  };

  const formatNumber = (num: number | string | undefined, decimals = 2) => {
    if (num === undefined || num === null || num === '') {
      return '0';
    }

    try {
      const numValue = parseFloat(num.toString());
      if (isNaN(numValue)) {
        return '0';
      }

      return numValue.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      });
    } catch (error) {
      console.warn('Error formatting number:', num, error);
      return '0';
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getChangeColor = (value: string | number | undefined) => {
    if (value === undefined || value === null || value === '') {
      return 'neutral';
    }

    try {
      const numValue = parseFloat(value.toString());
      if (isNaN(numValue)) {
        return 'neutral';
      }
      return numValue >= 0 ? 'positive' : 'negative';
    } catch {
      return 'neutral';
    }
  };

  const isValidValue = (value: any) => {
    return value !== undefined && value !== null && value !== '';
  };

  // ============================================
  // RENDER
  // ============================================

  return (
    <div className="app">
      {/* Debug Panel */}
      <DebugPanel
        currentBlock={currentBlock}
        mempoolData={mempoolData}
        connectionStatus={connectionStatus}
      />

      {/* Educational Panel */}
      <BlockchainEducationalPanel
        isOpen={showEducationalPanel}
        onToggle={() => setShowEducationalPanel(!showEducationalPanel)}
      />

      <div className="header">
        <h1>🚀 Multi-Blockchain Real-Time Monitor</h1>

        {/* Connection Status */}
        <div className="connection-status">
          <div className={`status-item ${connectionStatus.server ? 'connected' : 'disconnected'}`}>
            Server: {connectionStatus.server ? '🟢' : '🔴'}
          </div>

          <div className={`status-item ${connectionStatus.blockchain ? 'connected' : 'disconnected'}`}>
            Blockchain: {connectionStatus.blockchain ? '🟢' : '🔴'}
          </div>
          <div className="last-update">
            Last Update: {formatTimestamp(lastUpdate)}
          </div>
        </div>

        {/* Error Message */}
        {connectionError && (
          <div className="error-message">
            ⚠️ {connectionError}
            <button onClick={() => window.location.reload()} className="retry-btn">
              🔄 Retry Connection
            </button>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="tab-navigation">
          <button
            className={`tab-btn ${activeTab === 'blockchain' ? 'active' : ''}`}
            onClick={() => setActiveTab('blockchain')}
          >
            ⛓️ Bitcoin Blockchain
          </button>
          <button
            className={`tab-btn ${activeTab === 'ethereum' ? 'active' : ''}`}
            onClick={() => setActiveTab('ethereum')}
          >
            🔷 Ethereum Network
          </button>
        </div>
      </div>



      {/* ============================================ */}
      {/* BLOCKCHAIN TAB - USANDO EL COMPONENTE MEJORADO */}
      {/* ============================================ */}

      {activeTab === 'blockchain' && (
        <EnhancedBlockchainTab
          currentBlock={currentBlock}
          mempoolData={mempoolData}
          recentTransactions={recentTransactions}
          networkMetrics={networkMetrics}
          whaleActivity={whaleActivity}
          predictions={predictions}
          refreshBlockchain={refreshBlockchain}
          showEducationalPanel={showEducationalPanel}
          setShowEducationalPanel={setShowEducationalPanel}
        />
      )}

      {/* ============================================ */}
      {/* ETHEREUM TAB */}
      {/* ============================================ */}

      {activeTab === 'ethereum' && (
        <EthereumTab
          ethereumData={ethereumData}
          refreshEthereum={refreshEthereum}
          showEducationalPanel={showEducationalPanel}
          setShowEducationalPanel={setShowEducationalPanel}
        />
      )}
    </div>
  )
}

export default App
