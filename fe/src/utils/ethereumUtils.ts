import { ethers } from 'ethers';

// ============================================
// FUNCIONES DE UTILIDAD PARA ETHEREUM
// ============================================

// Formatear Wei a Ether
export const formatEther = (wei: string | number | bigint): string => {
  try {
    if (!wei) return '0';
    return ethers.formatEther(wei.toString());
  } catch (error) {
    console.error('Error formatting ether:', error);
    return '0';
  }
};

// Formatear Ether a Wei
export const parseEther = (ether: string): string => {
  try {
    return ethers.parseEther(ether).toString();
  } catch (error) {
    console.error('Error parsing ether:', error);
    return '0';
  }
};

// Formatear Gwei
export const formatGwei = (wei: string | number | bigint): string => {
  try {
    if (!wei) return '0';
    return ethers.formatUnits(wei.toString(), 'gwei');
  } catch (error) {
    console.error('Error formatting gwei:', error);
    return '0';
  }
};

// Validar dirección de Ethereum
export const isValidAddress = (address: string): boolean => {
  try {
    return ethers.isAddress(address);
  } catch (error) {
    return false;
  }
};

// Formatear dirección (mostrar solo primeros y últimos caracteres)
export const formatAddress = (address: string, startChars = 6, endChars = 4): string => {
  if (!address || !isValidAddress(address)) return 'Invalid Address';
  if (address.length <= startChars + endChars) return address;
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

// Formatear hash de transacción
export const formatTxHash = (hash: string, startChars = 8, endChars = 6): string => {
  if (!hash) return 'Invalid Hash';
  if (hash.length <= startChars + endChars) return hash;
  return `${hash.slice(0, startChars)}...${hash.slice(-endChars)}`;
};

// Formatear número de bloque
export const formatBlockNumber = (blockNumber: number | string): string => {
  try {
    const num = typeof blockNumber === 'string' ? parseInt(blockNumber) : blockNumber;
    return num.toLocaleString();
  } catch (error) {
    return '0';
  }
};

// Formatear timestamp de Ethereum
export const formatEthereumTimestamp = (timestamp: number): string => {
  try {
    return new Date(timestamp * 1000).toLocaleString('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

// Calcular tiempo transcurrido desde timestamp
export const getTimeAgo = (timestamp: number): string => {
  try {
    const now = Math.floor(Date.now() / 1000);
    const diff = now - timestamp;
    
    if (diff < 60) return `${diff}s ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
    if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`;
    return `${Math.floor(diff / 86400)}d ago`;
  } catch (error) {
    return 'Unknown';
  }
};

// Formatear gas usado vs límite
export const formatGasUsage = (gasUsed: number | string, gasLimit: number | string): string => {
  try {
    const used = typeof gasUsed === 'string' ? parseInt(gasUsed) : gasUsed;
    const limit = typeof gasLimit === 'string' ? parseInt(gasLimit) : gasLimit;
    const percentage = ((used / limit) * 100).toFixed(1);
    return `${used.toLocaleString()} / ${limit.toLocaleString()} (${percentage}%)`;
  } catch (error) {
    return 'Invalid Gas Data';
  }
};

// Obtener estado del gas basado en el precio
export const getGasStatus = (gasPrice: string | number): { status: string; color: string; description: string } => {
  try {
    const priceGwei = parseFloat(formatGwei(gasPrice));
    
    if (priceGwei < 20) {
      return { status: 'Low', color: 'green', description: 'Gas barato' };
    } else if (priceGwei < 50) {
      return { status: 'Medium', color: 'yellow', description: 'Gas normal' };
    } else if (priceGwei < 100) {
      return { status: 'High', color: 'orange', description: 'Gas caro' };
    } else {
      return { status: 'Very High', color: 'red', description: 'Gas muy caro' };
    }
  } catch (error) {
    return { status: 'Unknown', color: 'gray', description: 'Gas desconocido' };
  }
};

// Estimar tiempo de confirmación basado en gas price
export const estimateConfirmationTime = (gasPrice: string | number): string => {
  try {
    const priceGwei = parseFloat(formatGwei(gasPrice));
    
    if (priceGwei >= 50) return '~1-2 min (Rápido)';
    if (priceGwei >= 30) return '~2-5 min (Medio)';
    if (priceGwei >= 15) return '~5-10 min (Lento)';
    return '>10 min (Muy lento)';
  } catch (error) {
    return 'Tiempo desconocido';
  }
};

// Formatear valor en USD (asumiendo precio de ETH)
export const formatUSDValue = (ethAmount: string, ethPriceUSD: number): string => {
  try {
    const eth = parseFloat(formatEther(ethAmount));
    const usdValue = eth * ethPriceUSD;
    return `$${usdValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  } catch (error) {
    return '$0.00';
  }
};

// Detectar tipo de transacción basado en datos
export const getTransactionType = (tx: any): string => {
  if (!tx) return 'Unknown';
  
  if (tx.to === null) return 'Contract Creation';
  if (tx.value && parseFloat(formatEther(tx.value)) > 0) return 'ETH Transfer';
  if (tx.data && tx.data !== '0x') return 'Contract Interaction';
  return 'Transaction';
};

// Formatear datos de log/evento
export const formatLogData = (log: any): { event: string; contract: string; data: string } => {
  try {
    return {
      event: log.topics?.[0] ? `${log.topics[0].slice(0, 10)}...` : 'Unknown Event',
      contract: formatAddress(log.address),
      data: log.data ? `${log.data.slice(0, 20)}...` : 'No Data'
    };
  } catch (error) {
    return {
      event: 'Invalid Event',
      contract: 'Invalid Contract',
      data: 'Invalid Data'
    };
  }
};

// Calcular fee total de transacción
export const calculateTransactionFee = (gasUsed: string | number, gasPrice: string | number): string => {
  try {
    const used = typeof gasUsed === 'string' ? parseInt(gasUsed) : gasUsed;
    const price = typeof gasPrice === 'string' ? parseInt(gasPrice) : gasPrice;
    const feeWei = used * price;
    return formatEther(feeWei.toString());
  } catch (error) {
    return '0';
  }
};

// Obtener color para el estado de la red
export const getNetworkStatusColor = (pendingTxCount: number): string => {
  if (pendingTxCount < 1000) return 'green';
  if (pendingTxCount < 5000) return 'yellow';
  if (pendingTxCount < 10000) return 'orange';
  return 'red';
};

// Formatear número grande con sufijos
export const formatLargeNumber = (num: number): string => {
  if (num >= 1e9) return `${(num / 1e9).toFixed(1)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(1)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(1)}K`;
  return num.toString();
};

// Validar y formatear filtro de logs
export const formatLogFilter = (filter: any): any => {
  const formatted: any = {};
  
  if (filter.address) {
    formatted.address = Array.isArray(filter.address) 
      ? filter.address.filter(isValidAddress)
      : isValidAddress(filter.address) ? filter.address : undefined;
  }
  
  if (filter.topics) {
    formatted.topics = filter.topics;
  }
  
  if (filter.fromBlock) {
    formatted.fromBlock = filter.fromBlock;
  }
  
  if (filter.toBlock) {
    formatted.toBlock = filter.toBlock;
  }
  
  return formatted;
};

// Obtener icono para tipo de transacción
export const getTransactionIcon = (type: string): string => {
  switch (type) {
    case 'ETH Transfer': return '💸';
    case 'Contract Creation': return '📄';
    case 'Contract Interaction': return '⚙️';
    default: return '📝';
  }
};

// Formatear porcentaje de utilización de gas del bloque
export const formatBlockGasUtilization = (gasUsed: number | string, gasLimit: number | string): {
  percentage: number;
  status: string;
  color: string;
} => {
  try {
    const used = typeof gasUsed === 'string' ? parseInt(gasUsed) : gasUsed;
    const limit = typeof gasLimit === 'string' ? parseInt(gasLimit) : gasLimit;
    const percentage = (used / limit) * 100;
    
    let status = 'Normal';
    let color = 'green';
    
    if (percentage > 95) {
      status = 'Full';
      color = 'red';
    } else if (percentage > 80) {
      status = 'High';
      color = 'orange';
    } else if (percentage > 50) {
      status = 'Medium';
      color = 'yellow';
    }
    
    return { percentage: Math.round(percentage), status, color };
  } catch (error) {
    return { percentage: 0, status: 'Unknown', color: 'gray' };
  }
};
