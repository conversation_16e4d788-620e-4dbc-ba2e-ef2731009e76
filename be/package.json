{"name": "bitcoin-trading-backend", "version": "1.0.0", "description": "Real-time Bitcoin trading data backend", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase' > docs/file-system.md"}, "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "ethers": "^6.15.0", "express": "^4.21.2", "node-fetch": "^3.3.2", "socket.io": "^4.8.1", "web3": "^4.16.0", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["blockchain", "bitcoin", "websocket", "real-time", "mempool", "transactions"], "author": "", "license": "MIT", "engines": {"node": ">=16.0.0"}}