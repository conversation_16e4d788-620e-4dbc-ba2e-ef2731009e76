const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

// Importar servicios
const BlockchainService = require('./src/services/blockchainService');
const EthereumRpcService = require('./src/services/ethereumRpcService');

// ============================================
// CONFIGURACIÓN DEL SERVIDOR
// ============================================

const app = express();
const server = http.createServer(app);

const io = socketIo(server, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// ============================================
// INICIALIZACIÓN DE SERVICIOS
// ============================================

const blockchainService = new BlockchainService();
const ethereumRpcService = new EthereumRpcService();

let connectedClients = 0;
let serverStats = {
  startTime: Date.now(),
  blockchainUpdates: 0,
  totalMessages: 0
};



// ============================================
// CONFIGURACIÓN DE CALLBACKS PARA BLOCKCHAIN
// ============================================

blockchainService.setCallbacks({
  onBlockUpdate: (blockData, isNewBlock) => {
    serverStats.blockchainUpdates++;
    serverStats.totalMessages++;

    if (isNewBlock) {
      // Nuevo bloque minado - evento especial
      io.emit('new-block', blockData);
      io.emit('new-block-realtime', blockData);
      console.log(`⛏️ NEW BITCOIN BLOCK MINED! #${blockData.height} - ${blockData.txCount} transactions`);
    }

    // Emitir actualización general de blockchain
    io.emit('blockchain-block', blockData);
  },

  onMempoolUpdate: (mempoolData) => {
    io.emit('blockchain-mempool', mempoolData);
    console.log(`📊 Mempool: ${mempoolData.count || 'N/A'} pending transactions`);
  },

  onNewTransaction: (txData) => {
    io.emit('blockchain-transaction', txData);
    // No log individual de transacciones para evitar spam
  },

  onConnectionChange: (status) => {
    io.emit('blockchain-connection', status);
    console.log(`🔗 Blockchain Connection: ${status.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
  },

  onPredictionUpdate: (predictions) => {
    io.emit('blockchain-predictions', predictions);
    console.log(`🔮 Prediction Update: ${predictions.interpretation} (${Math.round(predictions.confidence)}% confidence)`);
  },

  onWhaleAlert: (alert) => {
    io.emit('whale-alert', alert);
    console.log(`🐋 WHALE ALERT: ${alert.message}`);
  }
});

// ============================================
// CONFIGURACIÓN DE CALLBACKS PARA ETHEREUM
// ============================================

ethereumRpcService.setCallbacks({
  onNewBlock: (blockData) => {
    serverStats.totalMessages++;
    io.emit('ethereum-new-block', blockData);
    console.log(`⛏️ NEW ETHEREUM BLOCK! #${blockData.number} - Gas Used: ${blockData.gasUsed}/${blockData.gasLimit}`);
  },

  onNewTransaction: (txHash) => {
    serverStats.totalMessages++;
    io.emit('ethereum-new-transaction', txHash);
  },

  onNewLog: (logData) => {
    serverStats.totalMessages++;
    io.emit('ethereum-new-log', logData);
  },

  onGasPriceUpdate: (gasPriceData) => {
    serverStats.totalMessages++;
    io.emit('ethereum-gas-price', gasPriceData);
  }
});

// ============================================
// CICLOS DE ACTUALIZACIÓN
// ============================================

// Actualizar datos de Blockchain cada 30 segundos
setInterval(async () => {
  try {
    const blockchainData = await blockchainService.updateAllData();
    // Emitir datos completos de blockchain
    io.emit('blockchain-data', blockchainData);
  } catch (error) {
    console.error('❌ Error in Blockchain update cycle:', error.message);
  }
}, 30000);

// Stats del servidor cada 60 segundos
setInterval(() => {
  const uptime = Date.now() - serverStats.startTime;
  console.log(`📈 Server Stats: ${connectedClients} clients | ${serverStats.blockchainUpdates} Blockchain updates | Uptime: ${Math.floor(uptime / 60000)}m`);
}, 60000);

// ============================================
// INICIALIZACIÓN DE SERVICIOS
// ============================================

async function initializeServices() {
  console.log('🚀 Initializing services...');

  try {
    // Inicializar datos iniciales de Bitcoin
    console.log('📊 Fetching initial Bitcoin data...');
    await blockchainService.updateAllData();

    // Conectar WebSockets de Bitcoin
    console.log('🔌 Connecting Bitcoin WebSockets...');
    blockchainService.connectWebSocket();

    // Inicializar Ethereum RPC Service
    console.log('🔗 Initializing Ethereum RPC Service...');
    await ethereumRpcService.initializeProvider();

    // Obtener datos iniciales de Ethereum
    console.log('📊 Fetching initial Ethereum data...');
    await ethereumRpcService.getNetworkStats();
    await ethereumRpcService.getGasPrice();

    console.log('✅ All services initialized successfully!');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
  }
}

// Ejecutar inicialización
initializeServices();

// ============================================
// SOCKET.IO CONNECTION HANDLING
// ============================================

io.on('connection', (socket) => {
  connectedClients++;
  console.log(`👤 Client connected. Total: ${connectedClients}`);

  // Enviar datos actuales inmediatamente
  const currentData = {
    blockchain: blockchainService.getAllData(),
    ethereum: ethereumRpcService.getAllData(),
    server: {
      ...serverStats,
      uptime: Date.now() - serverStats.startTime,
      connectedClients
    }
  };

  // Enviar datos completos
  socket.emit('initial-data', currentData);

  if (currentData.blockchain) {
    socket.emit('blockchain-data', currentData.blockchain);
  }

  if (currentData.ethereum) {
    socket.emit('ethereum-data', currentData.ethereum);
  }

  // Manejar solicitudes específicas del cliente

  socket.on('request-blockchain-refresh', async () => {
    try {
      const data = await blockchainService.updateAllData();
      socket.emit('blockchain-data', data);
    } catch (error) {
      socket.emit('error', { type: 'blockchain-refresh', message: error.message });
    }
  });

  socket.on('request-predictions', async () => {
    try {
      const predictions = blockchainService.getPredictions();
      socket.emit('blockchain-predictions', predictions);
    } catch (error) {
      socket.emit('error', { type: 'predictions', message: error.message });
    }
  });

  socket.on('request-whale-activity', async () => {
    try {
      const whaleActivity = blockchainService.getWhaleActivity();
      socket.emit('whale-activity', whaleActivity);
    } catch (error) {
      socket.emit('error', { type: 'whale-activity', message: error.message });
    }
  });

  socket.on('request-network-metrics', async () => {
    try {
      const networkMetrics = blockchainService.getNetworkMetrics();
      socket.emit('network-metrics', networkMetrics);
    } catch (error) {
      socket.emit('error', { type: 'network-metrics', message: error.message });
    }
  });

  // ============================================
  // ETHEREUM WEBSOCKET EVENTS
  // ============================================

  socket.on('request-ethereum-data', async () => {
    try {
      const ethereumData = ethereumRpcService.getAllData();
      socket.emit('ethereum-data', ethereumData);
    } catch (error) {
      socket.emit('error', { type: 'ethereum-data', message: error.message });
    }
  });

  socket.on('request-ethereum-block', async (blockNumber = 'latest') => {
    try {
      const block = await ethereumRpcService.eth_getBlockByNumber(blockNumber, true);
      socket.emit('ethereum-block', block);
    } catch (error) {
      socket.emit('error', { type: 'ethereum-block', message: error.message });
    }
  });

  socket.on('request-ethereum-balance', async (address) => {
    try {
      const balance = await ethereumRpcService.eth_getBalance(address);
      socket.emit('ethereum-balance', { address, balance });
    } catch (error) {
      socket.emit('error', { type: 'ethereum-balance', message: error.message });
    }
  });

  socket.on('request-ethereum-gas-price', async () => {
    try {
      const gasPrice = await ethereumRpcService.getGasPrice();
      socket.emit('ethereum-gas-price', gasPrice);
    } catch (error) {
      socket.emit('error', { type: 'ethereum-gas-price', message: error.message });
    }
  });

  socket.on('request-ethereum-logs', async (filter) => {
    try {
      const logs = await ethereumRpcService.eth_getLogs(filter);
      socket.emit('ethereum-logs', logs);
    } catch (error) {
      socket.emit('error', { type: 'ethereum-logs', message: error.message });
    }
  });

  socket.on('ethereum-subscribe-logs', (filter) => {
    try {
      // Nota: En una implementación real, esto requeriría gestión de suscripciones por cliente
      console.log(`📡 Client subscribed to Ethereum logs with filter:`, filter);
      socket.emit('ethereum-subscription-confirmed', { type: 'logs', filter });
    } catch (error) {
      socket.emit('error', { type: 'ethereum-subscribe', message: error.message });
    }
  });

  socket.on('ethereum-unsubscribe', (subscriptionId) => {
    try {
      console.log(`📡 Client unsubscribed from Ethereum subscription:`, subscriptionId);
      socket.emit('ethereum-unsubscription-confirmed', { subscriptionId });
    } catch (error) {
      socket.emit('error', { type: 'ethereum-unsubscribe', message: error.message });
    }
  });

  // Cliente desconectado
  socket.on('disconnect', (reason) => {
    connectedClients--;
    console.log(`👤 Client disconnected (${reason}). Total: ${connectedClients}`);
  });
});

// ============================================
// REST API ENDPOINTS
// ============================================

// Health check general
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    uptime: Date.now() - serverStats.startTime,
    connected_clients: connectedClients,
    services: {
      blockchain: blockchainService.getConnectionStatus()
    },
    stats: serverStats
  });
});



// Endpoints específicos de Blockchain
app.get('/api/blockchain', (req, res) => {
  res.json(blockchainService.getAllData());
});

app.get('/api/blockchain/block', (req, res) => {
  const blockData = blockchainService.getCurrentBlock();
  if (blockData) {
    res.json(blockData);
  } else {
    res.status(503).json({ error: 'Block data not available' });
  }
});

app.get('/api/blockchain/mempool', (req, res) => {
  const mempoolData = blockchainService.getMempoolInfo();
  res.json(mempoolData);
});

app.get('/api/blockchain/transactions', (req, res) => {
  const transactions = blockchainService.getRecentTransactions();
  res.json(transactions);
});

// Endpoint para predicciones
app.get('/api/blockchain/predictions', (req, res) => {
  const predictions = blockchainService.getPredictions();
  res.json(predictions || { error: 'Predictions not available yet' });
});

// Endpoint para actividad de ballenas
app.get('/api/blockchain/whales', (req, res) => {
  const whaleActivity = blockchainService.getWhaleActivity();
  res.json(whaleActivity || { error: 'Whale data not available yet' });
});

// Endpoint para métricas de red
app.get('/api/blockchain/network', (req, res) => {
  const networkMetrics = blockchainService.getNetworkMetrics();
  res.json(networkMetrics || { error: 'Network metrics not available yet' });
});

// Endpoint combinado con todas las predicciones
app.get('/api/blockchain/analytics', (req, res) => {
  const data = blockchainService.getAllData();
  res.json({
    predictions: data.predictions || {},
    whaleActivity: data.whaleActivity || {},
    networkMetrics: data.networkMetrics || {},
    sentiment: data.predictions?.sentiment || {},
    timestamp: Date.now()
  });
});

// Endpoint combinado (compatibilidad)
app.get('/api/all', (req, res) => {
  res.json({
    blockchain: blockchainService.getAllData(),
    ethereum: ethereumRpcService.getAllData(),
    server: {
      ...serverStats,
      uptime: Date.now() - serverStats.startTime,
      connectedClients
    }
  });
});

// ============================================
// ETHEREUM RPC ENDPOINTS
// ============================================

// Información general de Ethereum
app.get('/api/ethereum', (req, res) => {
  res.json(ethereumRpcService.getAllData());
});

// Número de bloque actual
app.get('/api/ethereum/blockNumber', async (req, res) => {
  try {
    const blockNumber = await ethereumRpcService.eth_blockNumber();
    res.json({ blockNumber });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Obtener bloque por número
app.get('/api/ethereum/block/:blockNumber?', async (req, res) => {
  try {
    const blockNumber = req.params.blockNumber || 'latest';
    const includeTransactions = req.query.includeTransactions === 'true';
    const block = await ethereumRpcService.eth_getBlockByNumber(blockNumber, includeTransactions);
    res.json(block);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Obtener balance de una dirección
app.get('/api/ethereum/balance/:address', async (req, res) => {
  try {
    const { address } = req.params;
    const blockTag = req.query.blockTag || 'latest';
    const balance = await ethereumRpcService.eth_getBalance(address, blockTag);
    res.json({ address, balance, blockTag });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Realizar una llamada a contrato
app.post('/api/ethereum/call', async (req, res) => {
  try {
    const { transaction, blockTag = 'latest' } = req.body;
    const result = await ethereumRpcService.eth_call(transaction, blockTag);
    res.json({ result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Obtener logs/eventos
app.post('/api/ethereum/logs', async (req, res) => {
  try {
    const filter = req.body;
    const logs = await ethereumRpcService.eth_getLogs(filter);
    res.json({ logs });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Estimar gas para una transacción
app.post('/api/ethereum/estimateGas', async (req, res) => {
  try {
    const transaction = req.body;
    const gasEstimate = await ethereumRpcService.eth_estimateGas(transaction);
    res.json({ gasEstimate });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Enviar transacción firmada
app.post('/api/ethereum/sendRawTransaction', async (req, res) => {
  try {
    const { signedTransaction } = req.body;
    const txHash = await ethereumRpcService.eth_sendRawTransaction(signedTransaction);
    res.json({ txHash });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Obtener información de una transacción
app.get('/api/ethereum/transaction/:txHash', async (req, res) => {
  try {
    const { txHash } = req.params;
    const transaction = await ethereumRpcService.getTransaction(txHash);
    res.json(transaction);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Obtener recibo de una transacción
app.get('/api/ethereum/receipt/:txHash', async (req, res) => {
  try {
    const { txHash } = req.params;
    const receipt = await ethereumRpcService.getTransactionReceipt(txHash);
    res.json(receipt);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Precio del gas actual
app.get('/api/ethereum/gasPrice', async (req, res) => {
  try {
    const gasPrice = await ethereumRpcService.getGasPrice();
    res.json(gasPrice);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Estadísticas de la red
app.get('/api/ethereum/networkStats', async (req, res) => {
  try {
    const stats = await ethereumRpcService.getNetworkStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Transacciones pendientes
app.get('/api/ethereum/pendingTransactions', (req, res) => {
  const pendingTxs = ethereumRpcService.getPendingTransactions();
  res.json({ pendingTransactions: pendingTxs });
});

// Logs recientes
app.get('/api/ethereum/recentLogs', (req, res) => {
  const logs = ethereumRpcService.getRecentLogs();
  res.json({ logs });
});

// ============================================
// ETHEREUM TRACING ENDPOINTS (Avanzados)
// ============================================

// Trace de un bloque
app.get('/api/ethereum/trace/block/:blockNumber', async (req, res) => {
  try {
    const { blockNumber } = req.params;
    const trace = await ethereumRpcService.trace_block(blockNumber);
    res.json({ trace });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Trace de una transacción
app.get('/api/ethereum/trace/transaction/:txHash', async (req, res) => {
  try {
    const { txHash } = req.params;
    const trace = await ethereumRpcService.trace_transaction(txHash);
    res.json({ trace });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Debug trace de una transacción
app.post('/api/ethereum/debug/traceTransaction/:txHash', async (req, res) => {
  try {
    const { txHash } = req.params;
    const options = req.body || {};
    const trace = await ethereumRpcService.debug_traceTransaction(txHash, options);
    res.json({ trace });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ============================================
// SERVER STARTUP
// ============================================

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Multi-Blockchain Trading Server running on port ${PORT}`);
  console.log(`🔗 WebSocket endpoint: ws://localhost:${PORT}`);
  console.log(`🌐 API endpoint: http://localhost:${PORT}/api`);
  console.log(`⛓️ Bitcoin WebSocket: Enabled`);
  console.log(`🔷 Ethereum RPC: Enabled`);
  console.log(`⚡ Real-time data updates: Active`);
  console.log(`📊 Available endpoints:`);
  console.log(`   - Bitcoin: /api/blockchain/*`);
  console.log(`   - Ethereum: /api/ethereum/*`);
});

// ============================================
// GRACEFUL SHUTDOWN
// ============================================

function gracefulShutdown(signal) {
  console.log(`🔴 Received ${signal}. Shutting down gracefully...`);

  // Desconectar servicios
  blockchainService.disconnect();
  ethereumRpcService.disconnect();

  // Cerrar servidor
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });

  // Forzar cierre después de 10 segundos
  setTimeout(() => {
    console.log('⚠️ Forcing shutdown...');
    process.exit(1);
  }, 10000);
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Manejar errores no capturados
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});
