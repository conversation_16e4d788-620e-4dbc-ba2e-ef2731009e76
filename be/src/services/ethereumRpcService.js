const { ethers } = require('ethers');
const axios = require('axios');
const WebSocket = require('ws');

// ============================================
// ETHEREUM RPC SERVICE
// ============================================

class EthereumRpcService {
  constructor() {
    // Configuración de proveedores RPC
    this.rpcEndpoints = [
      'https://eth-mainnet.g.alchemy.com/v2/demo', // Demo endpoint
      'https://mainnet.infura.io/v3/********************************', // Demo endpoint
      'https://ethereum.publicnode.com',
      'https://rpc.ankr.com/eth'
    ];

    this.currentEndpointIndex = 0;
    this.provider = null;
    this.wsProvider = null;
    this.wsConnection = null;

    // Cache para optimizar consultas
    this.cache = new Map();
    this.cacheTimeout = 30000; // 30 segundos

    // Datos en tiempo real
    this.realtimeData = {
      latestBlock: null,
      pendingTransactions: [],
      logs: [],
      gasPrice: null,
      networkStats: null
    };

    // Callbacks para eventos
    this.callbacks = {
      onNewBlock: null,
      onNewTransaction: null,
      onNewLog: null,
      onGasPriceUpdate: null
    };

    this.initializeProvider();
  }

  // ============================================
  // INICIALIZACIÓN Y CONFIGURACIÓN
  // ============================================

  async initializeProvider() {
    try {
      const endpoint = this.rpcEndpoints[this.currentEndpointIndex];
      console.log(`🔗 Connecting to Ethereum RPC: ${endpoint}`);

      this.provider = new ethers.JsonRpcProvider(endpoint);

      // Verificar conexión
      const network = await this.provider.getNetwork();
      console.log(`✅ Connected to Ethereum network: ${network.name} (Chain ID: ${network.chainId})`);

      // Inicializar WebSocket si está disponible
      this.initializeWebSocket();

      return true;
    } catch (error) {
      console.error(`❌ Failed to connect to ${this.rpcEndpoints[this.currentEndpointIndex]}:`, error.message);

      // Intentar con el siguiente endpoint
      this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;

      if (this.currentEndpointIndex === 0) {
        console.error('❌ All RPC endpoints failed');
        return false;
      }

      return this.initializeProvider();
    }
  }

  initializeWebSocket() {
    // Para WebSocket, usaremos endpoints específicos que soporten WSS
    const wsEndpoints = [
      'wss://eth-mainnet.g.alchemy.com/v2/demo',
      'wss://mainnet.infura.io/ws/v3/********************************'
    ];

    try {
      const wsEndpoint = wsEndpoints[0];
      console.log(`🔌 Connecting to Ethereum WebSocket: ${wsEndpoint}`);

      this.wsConnection = new WebSocket(wsEndpoint);

      this.wsConnection.on('open', () => {
        console.log('✅ Ethereum WebSocket connected');
        this.subscribeToEvents();
      });

      this.wsConnection.on('message', (data) => {
        this.handleWebSocketMessage(JSON.parse(data.toString()));
      });

      this.wsConnection.on('error', (error) => {
        console.error('❌ Ethereum WebSocket error:', error.message);
      });

      this.wsConnection.on('close', () => {
        console.log('🔌 Ethereum WebSocket disconnected, attempting to reconnect...');
        setTimeout(() => this.initializeWebSocket(), 5000);
      });

    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error.message);
    }
  }

  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // ============================================
  // MÉTODOS RPC BÁSICOS
  // ============================================

  async eth_blockNumber() {
    try {
      const cacheKey = 'blockNumber';
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const blockNumber = await this.provider.getBlockNumber();
      this.setCache(cacheKey, blockNumber, 10000); // Cache por 10 segundos

      return blockNumber;
    } catch (error) {
      console.error('❌ Error getting block number:', error.message);
      throw error;
    }
  }

  async eth_getBlockByNumber(blockNumber = 'latest', includeTransactions = false) {
    try {
      const cacheKey = `block_${blockNumber}_${includeTransactions}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const block = await this.provider.getBlock(blockNumber, includeTransactions);
      this.setCache(cacheKey, block, 30000); // Cache por 30 segundos

      return block;
    } catch (error) {
      console.error('❌ Error getting block:', error.message);
      throw error;
    }
  }

  async eth_getBalance(address, blockTag = 'latest') {
    try {
      const cacheKey = `balance_${address}_${blockTag}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const balance = await this.provider.getBalance(address, blockTag);
      this.setCache(cacheKey, balance.toString(), 15000); // Cache por 15 segundos

      return balance.toString();
    } catch (error) {
      console.error('❌ Error getting balance:', error.message);
      throw error;
    }
  }

  async eth_call(transaction, blockTag = 'latest') {
    try {
      const result = await this.provider.call(transaction, blockTag);
      return result;
    } catch (error) {
      console.error('❌ Error making eth_call:', error.message);
      throw error;
    }
  }

  // ============================================
  // MÉTODOS DE LOGS Y EVENTOS
  // ============================================

  async eth_getLogs(filter) {
    try {
      const logs = await this.provider.getLogs(filter);
      return logs;
    } catch (error) {
      console.error('❌ Error getting logs:', error.message);
      throw error;
    }
  }

  // ============================================
  // MÉTODOS DE TRANSACCIONES
  // ============================================

  async eth_estimateGas(transaction) {
    try {
      const gasEstimate = await this.provider.estimateGas(transaction);
      return gasEstimate.toString();
    } catch (error) {
      console.error('❌ Error estimating gas:', error.message);
      throw error;
    }
  }

  async eth_sendRawTransaction(signedTransaction) {
    try {
      const txResponse = await this.provider.broadcastTransaction(signedTransaction);
      return txResponse.hash;
    } catch (error) {
      console.error('❌ Error sending transaction:', error.message);
      throw error;
    }
  }

  async getTransaction(txHash) {
    try {
      const tx = await this.provider.getTransaction(txHash);
      return tx;
    } catch (error) {
      console.error('❌ Error getting transaction:', error.message);
      throw error;
    }
  }

  async getTransactionReceipt(txHash) {
    try {
      const receipt = await this.provider.getTransactionReceipt(txHash);
      return receipt;
    } catch (error) {
      console.error('❌ Error getting transaction receipt:', error.message);
      throw error;
    }
  }

  // ============================================
  // MÉTODOS DE CACHE
  // ============================================

  setCache(key, value, timeout = this.cacheTimeout) {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      timeout
    });
  }

  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.timeout) {
      this.cache.delete(key);
      return null;
    }

    return cached.value;
  }

  clearCache() {
    this.cache.clear();
  }

  // ============================================
  // WEBSOCKET SUBSCRIPTIONS
  // ============================================

  subscribeToEvents() {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.log('⚠️ WebSocket not ready for subscriptions');
      return;
    }

    // Suscribirse a nuevos bloques
    this.subscribe('newHeads');

    // Suscribirse a transacciones pendientes
    this.subscribe('pendingTransactions');

    // Suscribirse a logs (eventos de contratos)
    this.subscribeLogs();
  }

  subscribe(type) {
    const subscriptionRequest = {
      id: Date.now(),
      method: 'eth_subscribe',
      params: [type]
    };

    this.wsConnection.send(JSON.stringify(subscriptionRequest));
    console.log(`📡 Subscribed to ${type}`);
  }

  subscribeLogs(filter = {}) {
    const subscriptionRequest = {
      id: Date.now(),
      method: 'eth_subscribe',
      params: ['logs', filter]
    };

    this.wsConnection.send(JSON.stringify(subscriptionRequest));
    console.log('📡 Subscribed to logs');
  }

  handleWebSocketMessage(message) {
    try {
      if (message.method === 'eth_subscription') {
        const { subscription, result } = message.params;

        // Nuevo bloque
        if (result.number) {
          this.handleNewBlock(result);
        }
        // Nueva transacción pendiente
        else if (typeof result === 'string' && result.startsWith('0x')) {
          this.handleNewPendingTransaction(result);
        }
        // Nuevo log/evento
        else if (result.topics) {
          this.handleNewLog(result);
        }
      }
    } catch (error) {
      console.error('❌ Error handling WebSocket message:', error.message);
    }
  }

  handleNewBlock(blockData) {
    console.log(`🆕 New Ethereum block: ${parseInt(blockData.number, 16)}`);

    this.realtimeData.latestBlock = {
      number: parseInt(blockData.number, 16),
      hash: blockData.hash,
      timestamp: parseInt(blockData.timestamp, 16),
      gasLimit: parseInt(blockData.gasLimit, 16),
      gasUsed: parseInt(blockData.gasUsed, 16),
      baseFeePerGas: blockData.baseFeePerGas ? parseInt(blockData.baseFeePerGas, 16) : null,
      transactions: blockData.transactions || []
    };

    if (this.callbacks.onNewBlock) {
      this.callbacks.onNewBlock(this.realtimeData.latestBlock);
    }
  }

  handleNewPendingTransaction(txHash) {
    console.log(`🔄 New pending transaction: ${txHash}`);

    // Mantener solo las últimas 100 transacciones pendientes
    this.realtimeData.pendingTransactions.unshift(txHash);
    if (this.realtimeData.pendingTransactions.length > 100) {
      this.realtimeData.pendingTransactions = this.realtimeData.pendingTransactions.slice(0, 100);
    }

    if (this.callbacks.onNewTransaction) {
      this.callbacks.onNewTransaction(txHash);
    }
  }

  handleNewLog(logData) {
    console.log(`📋 New log from contract: ${logData.address}`);

    const formattedLog = {
      address: logData.address,
      topics: logData.topics,
      data: logData.data,
      blockNumber: parseInt(logData.blockNumber, 16),
      transactionHash: logData.transactionHash,
      logIndex: parseInt(logData.logIndex, 16)
    };

    this.realtimeData.logs.unshift(formattedLog);
    if (this.realtimeData.logs.length > 50) {
      this.realtimeData.logs = this.realtimeData.logs.slice(0, 50);
    }

    if (this.callbacks.onNewLog) {
      this.callbacks.onNewLog(formattedLog);
    }
  }

  // ============================================
  // MÉTODOS DE TRACING Y DEBUG
  // ============================================

  async trace_block(blockNumber) {
    try {
      // Nota: Estos métodos requieren nodos con capacidades de tracing habilitadas
      const response = await axios.post(this.rpcEndpoints[this.currentEndpointIndex], {
        jsonrpc: '2.0',
        method: 'trace_block',
        params: [blockNumber],
        id: 1
      });

      return response.data.result;
    } catch (error) {
      console.error('❌ Error tracing block:', error.message);
      throw error;
    }
  }

  async trace_transaction(txHash) {
    try {
      const response = await axios.post(this.rpcEndpoints[this.currentEndpointIndex], {
        jsonrpc: '2.0',
        method: 'trace_transaction',
        params: [txHash],
        id: 1
      });

      return response.data.result;
    } catch (error) {
      console.error('❌ Error tracing transaction:', error.message);
      throw error;
    }
  }

  async debug_traceTransaction(txHash, options = {}) {
    try {
      const response = await axios.post(this.rpcEndpoints[this.currentEndpointIndex], {
        jsonrpc: '2.0',
        method: 'debug_traceTransaction',
        params: [txHash, options],
        id: 1
      });

      return response.data.result;
    } catch (error) {
      console.error('❌ Error debug tracing transaction:', error.message);
      throw error;
    }
  }

  // ============================================
  // MÉTODOS DE UTILIDAD Y ESTADÍSTICAS
  // ============================================

  async getGasPrice() {
    try {
      const gasPrice = await this.provider.getFeeData();

      this.realtimeData.gasPrice = {
        gasPrice: gasPrice.gasPrice?.toString(),
        maxFeePerGas: gasPrice.maxFeePerGas?.toString(),
        maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas?.toString(),
        timestamp: Date.now()
      };

      if (this.callbacks.onGasPriceUpdate) {
        this.callbacks.onGasPriceUpdate(this.realtimeData.gasPrice);
      }

      return this.realtimeData.gasPrice;
    } catch (error) {
      console.error('❌ Error getting gas price:', error.message);
      throw error;
    }
  }

  async getNetworkStats() {
    try {
      const [blockNumber, gasPrice, network] = await Promise.all([
        this.eth_blockNumber(),
        this.getGasPrice(),
        this.provider.getNetwork()
      ]);

      this.realtimeData.networkStats = {
        chainId: Number(network.chainId),
        networkName: network.name,
        latestBlock: blockNumber,
        gasPrice: gasPrice,
        pendingTransactionsCount: this.realtimeData.pendingTransactions.length,
        timestamp: Date.now()
      };

      return this.realtimeData.networkStats;
    } catch (error) {
      console.error('❌ Error getting network stats:', error.message);
      throw error;
    }
  }

  // ============================================
  // MÉTODOS PÚBLICOS PARA OBTENER DATOS
  // ============================================

  getAllData() {
    return {
      latestBlock: this.realtimeData.latestBlock,
      pendingTransactions: this.realtimeData.pendingTransactions,
      logs: this.realtimeData.logs,
      gasPrice: this.realtimeData.gasPrice,
      networkStats: this.realtimeData.networkStats,
      lastUpdate: Date.now()
    };
  }

  getLatestBlock() {
    return this.realtimeData.latestBlock;
  }

  getPendingTransactions() {
    return this.realtimeData.pendingTransactions;
  }

  getRecentLogs() {
    return this.realtimeData.logs;
  }

  // ============================================
  // CLEANUP
  // ============================================

  disconnect() {
    if (this.wsConnection) {
      this.wsConnection.close();
    }
    this.clearCache();
    console.log('🔌 Ethereum RPC Service disconnected');
  }
}

module.exports = EthereumRpcService;
